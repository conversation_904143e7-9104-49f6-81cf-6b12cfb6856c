import React from "react";
import Header from "./components/Header";
import Modal from "./components/Modal";
import Summary from "./components/Summary";
import { SummaryProvider } from "./context/SummaryContext";

const App = () => {
  return (
    <SummaryProvider>
      <div className="flex flex-col h-screen bg-gray-100">
        <Header />
        {/* Main content that scrolls normally */}
        <main className="flex-1 overflow-y-auto pt-18">
          <div className="container mx-auto px-8">
            {/* Upload cards positioned below header with p-8 spacing */}
            <div className="pt-8">
              <Modal />
            </div>
            <div className="mt-16">
              <Summary />
            </div>
          </div>
        </main>
      </div>
    </SummaryProvider>
  );
};

export default App;




